fileFormatVersion: 2
guid: 6f7e0371a1f07ea49b5d6fe4d634ae6c
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: CH_Chest
  - first:
      1: 100002
    second: CH_FingerIn0_L
  - first:
      1: 100004
    second: CH_FingerIn0_R
  - first:
      1: 100006
    second: CH_FingerIn1_L
  - first:
      1: 100008
    second: CH_FingerIn1_R
  - first:
      1: 100010
    second: CH_FingerIn2_L
  - first:
      1: 100012
    second: CH_FingerIn2_R
  - first:
      1: 100014
    second: CH_FingerMid0_L
  - first:
      1: 100016
    second: CH_FingerMid0_R
  - first:
      1: 100018
    second: CH_FingerMid1_L
  - first:
      1: 100020
    second: CH_FingerMid1_R
  - first:
      1: 100022
    second: CH_FingerMid2_L
  - first:
      1: 100024
    second: CH_FingerMid2_R
  - first:
      1: 100026
    second: CH_FingerP0_L
  - first:
      1: 100028
    second: CH_FingerP0_R
  - first:
      1: 100030
    second: CH_FingerP1_L
  - first:
      1: 100032
    second: CH_FingerP1_R
  - first:
      1: 100034
    second: CH_FingerP2_L
  - first:
      1: 100036
    second: CH_FingerP2_R
  - first:
      1: 100038
    second: CH_FingerR0_L
  - first:
      1: 100040
    second: CH_FingerR0_R
  - first:
      1: 100042
    second: CH_FingerR1_L
  - first:
      1: 100044
    second: CH_FingerR1_R
  - first:
      1: 100046
    second: CH_FingerR2_L
  - first:
      1: 100048
    second: CH_FingerR2_R
  - first:
      1: 100050
    second: CH_FingerTh0_L
  - first:
      1: 100052
    second: CH_FingerTh0_R
  - first:
      1: 100054
    second: CH_FingerTh1_L
  - first:
      1: 100056
    second: CH_FingerTh1_R
  - first:
      1: 100058
    second: CH_FingerTh2_L
  - first:
      1: 100060
    second: CH_FingerTh2_R
  - first:
      1: 100062
    second: CH_Foot_L
  - first:
      1: 100064
    second: CH_Foot_R
  - first:
      1: 100066
    second: CH_For_L
  - first:
      1: 100068
    second: CH_For_R
  - first:
      1: 100070
    second: CH_Hand_L
  - first:
      1: 100072
    second: CH_Hand_R
  - first:
      1: 100074
    second: CH_Head
  - first:
      1: 100076
    second: CH_Hips
  - first:
      1: 100078
    second: CH_Neck
  - first:
      1: 100080
    second: CH_Shin_L
  - first:
      1: 100082
    second: CH_Shin_R
  - first:
      1: 100084
    second: CH_Shou_L
  - first:
      1: 100086
    second: CH_Shou_R
  - first:
      1: 100088
    second: CH_Spine
  - first:
      1: 100090
    second: CH_Thigh_L
  - first:
      1: 100092
    second: CH_Thigh_R
  - first:
      1: 100094
    second: CH_Uppe_L
  - first:
      1: 100096
    second: CH_Uppe_R
  - first:
      1: 100098
    second: //RootNode
  - first:
      4: 400000
    second: CH_Chest
  - first:
      4: 400002
    second: CH_FingerIn0_L
  - first:
      4: 400004
    second: CH_FingerIn0_R
  - first:
      4: 400006
    second: CH_FingerIn1_L
  - first:
      4: 400008
    second: CH_FingerIn1_R
  - first:
      4: 400010
    second: CH_FingerIn2_L
  - first:
      4: 400012
    second: CH_FingerIn2_R
  - first:
      4: 400014
    second: CH_FingerMid0_L
  - first:
      4: 400016
    second: CH_FingerMid0_R
  - first:
      4: 400018
    second: CH_FingerMid1_L
  - first:
      4: 400020
    second: CH_FingerMid1_R
  - first:
      4: 400022
    second: CH_FingerMid2_L
  - first:
      4: 400024
    second: CH_FingerMid2_R
  - first:
      4: 400026
    second: CH_FingerP0_L
  - first:
      4: 400028
    second: CH_FingerP0_R
  - first:
      4: 400030
    second: CH_FingerP1_L
  - first:
      4: 400032
    second: CH_FingerP1_R
  - first:
      4: 400034
    second: CH_FingerP2_L
  - first:
      4: 400036
    second: CH_FingerP2_R
  - first:
      4: 400038
    second: CH_FingerR0_L
  - first:
      4: 400040
    second: CH_FingerR0_R
  - first:
      4: 400042
    second: CH_FingerR1_L
  - first:
      4: 400044
    second: CH_FingerR1_R
  - first:
      4: 400046
    second: CH_FingerR2_L
  - first:
      4: 400048
    second: CH_FingerR2_R
  - first:
      4: 400050
    second: CH_FingerTh0_L
  - first:
      4: 400052
    second: CH_FingerTh0_R
  - first:
      4: 400054
    second: CH_FingerTh1_L
  - first:
      4: 400056
    second: CH_FingerTh1_R
  - first:
      4: 400058
    second: CH_FingerTh2_L
  - first:
      4: 400060
    second: CH_FingerTh2_R
  - first:
      4: 400062
    second: CH_Foot_L
  - first:
      4: 400064
    second: CH_Foot_R
  - first:
      4: 400066
    second: CH_For_L
  - first:
      4: 400068
    second: CH_For_R
  - first:
      4: 400070
    second: CH_Hand_L
  - first:
      4: 400072
    second: CH_Hand_R
  - first:
      4: 400074
    second: CH_Head
  - first:
      4: 400076
    second: CH_Hips
  - first:
      4: 400078
    second: CH_Neck
  - first:
      4: 400080
    second: CH_Shin_L
  - first:
      4: 400082
    second: CH_Shin_R
  - first:
      4: 400084
    second: CH_Shou_L
  - first:
      4: 400086
    second: CH_Shou_R
  - first:
      4: 400088
    second: CH_Spine
  - first:
      4: 400090
    second: CH_Thigh_L
  - first:
      4: 400092
    second: CH_Thigh_R
  - first:
      4: 400094
    second: CH_Uppe_L
  - first:
      4: 400096
    second: CH_Uppe_R
  - first:
      4: 400098
    second: //RootNode
  - first:
      74: 7400000
    second: UC01_001_JAK01_003
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: UC01_001_JAK01_003
      takeName: UC01_001_JAK01_003
      internalID: 0
      firstFrame: 0
      lastFrame: 8628
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: UC01_001_JAK01_003 (short)
      takeName: UC01_001_JAK01_003
      internalID: -6150270346426837497
      firstFrame: 904
      lastFrame: 6104
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: UC01_001_INIT_POSE
      takeName: UC01_001_JAK01_003
      internalID: 5322403403441133380
      firstFrame: 0
      lastFrame: 33
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: CH_Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Thigh_L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Thigh_R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Shin_L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Shin_R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Foot_L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Foot_R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Shou_L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Shou_R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Uppe_L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Uppe_R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_For_L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_For_R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Hand_L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_Hand_R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerTh0_L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerTh1_L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerTh2_L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerIn0_L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerIn1_L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerIn2_L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerMid0_L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerMid1_L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerMid2_L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerR0_L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerR1_L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerR2_L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerP0_L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerP1_L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerP2_L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerTh0_R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerTh1_R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerTh2_R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerIn0_R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerIn1_R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerIn2_R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerMid0_R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerMid1_R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerMid2_R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerR0_R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerR1_R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerR2_R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerP0_R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerP1_R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: CH_FingerP2_R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: UC01_001_UniteInTheSky(Clone)
      parentName: 
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: CH_Hips
      parentName: UC01_001_UniteInTheSky(Clone)
      position: {x: -0.000072773546, y: 0.8797906, z: 0.070019096}
      rotation: {x: 0.008795875, y: 0.02702195, z: 0.0010814383, w: 0.9995956}
      scale: {x: 1, y: 1, z: 1}
    - name: CH_Spine
      parentName: CH_Hips
      position: {x: 1.4901161e-10, y: 0.05238224, z: 0.003530197}
      rotation: {x: -0.008975765, y: -0.0030821536, z: -0.0014939619, w: 0.99995387}
      scale: {x: 1, y: 1.0000005, z: 1.0000005}
    - name: CH_Chest
      parentName: CH_Spine
      position: {x: -1.862645e-10, y: 0.16491231, z: 0.006577148}
      rotation: {x: 0.01025283, y: -0.00003700082, z: -0.009826875, w: 0.99989915}
      scale: {x: 1, y: 0.99999976, z: 0.99999976}
    - name: CH_Neck
      parentName: CH_Chest
      position: {x: -0.0000000013783574, y: 0.24534911, z: -0.035706326}
      rotation: {x: -0.019598613, y: -0.0015617736, z: -0.0032422806, w: 0.99980146}
      scale: {x: 1, y: 1.0000005, z: 1.0000005}
    - name: CH_Head
      parentName: CH_Neck
      position: {x: -3.0527952e-10, y: 0.0970388, z: -0.0062348065}
      rotation: {x: 0.04305692, y: -0.0057739904, z: 0.006471334, w: 0.999035}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: CH_Shou_L
      parentName: CH_Chest
      position: {x: -0.012281969, y: 0.23065636, z: -0.023482818}
      rotation: {x: -0.058675427, y: -0.049061775, z: -0.14489128, w: 0.98648703}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: CH_Uppe_L
      parentName: CH_Shou_L
      position: {x: -0.10911131, y: -0.0052890014, z: -0.00843935}
      rotation: {x: -0.091201335, y: -0.009756492, z: 0.13481009, w: 0.98661715}
      scale: {x: 1.0000006, y: 1.0000001, z: 1.0000007}
    - name: CH_For_L
      parentName: CH_Uppe_L
      position: {x: -0.24872932, y: -0.0068639717, z: 0.006644745}
      rotation: {x: 0.06885329, y: 0.033358697, z: 0.067166924, w: 0.994804}
      scale: {x: 0.9999993, y: 1.0000004, z: 0.99999905}
    - name: CH_Hand_L
      parentName: CH_For_L
      position: {x: -0.21798706, y: 0.0024363708, z: -0.001611715}
      rotation: {x: 0.055273533, y: 0.022377921, z: 0.00855618, w: 0.9981838}
      scale: {x: 1.0000005, y: 0.9999999, z: 1.0000001}
    - name: CH_FingerIn0_L
      parentName: CH_Hand_L
      position: {x: -0.06406102, y: 0.007037311, z: 0.030024793}
      rotation: {x: -0.0721098, y: 0.058076605, z: -0.020419743, w: 0.995495}
      scale: {x: 0.9999995, y: 1.0000002, z: 0.9999999}
    - name: CH_FingerIn1_L
      parentName: CH_FingerIn0_L
      position: {x: -0.03864456, y: 0.001219635, z: 0.000000026226044}
      rotation: {x: -0.00000030174837, y: -0.0000016391269, z: 0.029626999, w: 0.9995611}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: CH_FingerIn2_L
      parentName: CH_FingerIn1_L
      position: {x: -0.019831618, y: 0.00048400878, z: 0.00000009298324}
      rotation: {x: 0, y: 0, z: 0.20096508, w: 0.9795984}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: CH_FingerMid0_L
      parentName: CH_Hand_L
      position: {x: -0.06557449, y: 0.011041107, z: 0.008100586}
      rotation: {x: 0.004228337, y: 0.037817065, z: -0.025989868, w: 0.9989377}
      scale: {x: 0.99999994, y: 1.0000002, z: 0.99999964}
    - name: CH_FingerMid1_L
      parentName: CH_FingerMid0_L
      position: {x: -0.041185796, y: -0.0007038879, z: -0.00014655113}
      rotation: {x: 0.0007604414, y: 0.00058571255, z: 0.052795976, w: 0.99860483}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: CH_FingerMid2_L
      parentName: CH_FingerMid1_L
      position: {x: -0.021683998, y: 0.00048583985, z: -0.00010738373}
      rotation: {x: 0, y: 0, z: 0.22624305, w: 0.9740709}
      scale: {x: 1, y: 0.9999997, z: 0.99999994}
    - name: CH_FingerP0_L
      parentName: CH_Hand_L
      position: {x: -0.052998427, y: 0.004468918, z: -0.02733322}
      rotation: {x: -0.01587042, y: 0.05055333, z: -0.03912815, w: 0.99782836}
      scale: {x: 0.99999976, y: 0.9999999, z: 1.0000001}
    - name: CH_FingerP1_L
      parentName: CH_FingerP0_L
      position: {x: -0.030735435, y: -0.0015520477, z: 0}
      rotation: {x: -0.00000013969839, y: -0.0000012814999, z: 0.07282637, w: 0.99734473}
      scale: {x: 1.0000006, y: 1.0000002, z: 1.0000005}
    - name: CH_FingerP2_L
      parentName: CH_FingerP1_L
      position: {x: -0.016274987, y: 0.00048400878, z: 0}
      rotation: {x: 0, y: 0, z: 0.25102088, w: 0.9679817}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
    - name: CH_FingerR0_L
      parentName: CH_Hand_L
      position: {x: -0.06365898, y: 0.01094162, z: -0.011445865}
      rotation: {x: -0.0060580475, y: 0.043660592, z: -0.030851023, w: 0.99855167}
      scale: {x: 0.99999994, y: 1.0000002, z: 1.0000002}
    - name: CH_FingerR1_L
      parentName: CH_FingerR0_L
      position: {x: -0.037883833, y: -0.0011924744, z: -0.000000038146972}
      rotation: {x: 0.0000004996544, y: 0.0000019446009, z: 0.04791813, w: 0.9988513}
      scale: {x: 0.9999999, y: 1.0000001, z: 1.0000001}
    - name: CH_FingerR2_L
      parentName: CH_FingerR1_L
      position: {x: -0.019339943, y: -0.00000015258789, z: -0.000000076293944}
      rotation: {x: 0, y: 0, z: 0.23865104, w: 0.9711054}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: CH_FingerTh0_L
      parentName: CH_Hand_L
      position: {x: -0.0064083505, y: -0.012739102, z: 0.020647034}
      rotation: {x: 0.6994364, y: 0.252594, z: -0.32049552, w: 0.58674324}
      scale: {x: 0.9999998, y: 1.0000002, z: 1}
    - name: CH_FingerTh1_L
      parentName: CH_FingerTh0_L
      position: {x: -0.04057995, y: -0.0009107208, z: 0.0018043518}
      rotation: {x: -0.0037802155, y: 0.018734846, z: 0.062868975, w: 0.99783885}
      scale: {x: 1.0000005, y: 0.99999994, z: 1.0000008}
    - name: CH_FingerTh2_L
      parentName: CH_FingerTh1_L
      position: {x: -0.022210578, y: 0.0008297729, z: 0}
      rotation: {x: 0, y: 0, z: 0.23422016, w: 0.9721836}
      scale: {x: 0.99999994, y: 0.99999976, z: 1.0000005}
    - name: CH_Shou_R
      parentName: CH_Chest
      position: {x: 0.012124489, y: 0.23065633, z: -0.0234831}
      rotation: {x: -0.17077981, y: 0.98363066, z: -0.056415632, w: -0.011055562}
      scale: {x: 1, y: 1.0000004, z: 1.0000004}
    - name: CH_Uppe_R
      parentName: CH_Shou_R
      position: {x: -0.109111294, y: -0.0052891704, z: 0.008439026}
      rotation: {x: 0.14516506, y: 0.04554295, z: 0.15130717, w: 0.9767083}
      scale: {x: 0.99999976, y: 1.0000013, z: 1.0000002}
    - name: CH_For_R
      parentName: CH_Uppe_R
      position: {x: -0.24872936, y: -0.006864166, z: -0.0066439817}
      rotation: {x: -0.06835322, y: -0.014880213, z: 0.05765317, w: 0.9958828}
      scale: {x: 0.99999994, y: 0.99999946, z: 0.9999995}
    - name: CH_Hand_R
      parentName: CH_For_R
      position: {x: -0.21798724, y: 0.0024362183, z: 0.0016114789}
      rotation: {x: -0.11417606, y: 0.015187072, z: -0.00067673635, w: 0.99334425}
      scale: {x: 1.000001, y: 1.0000005, z: 1}
    - name: CH_FingerIn0_R
      parentName: CH_Hand_R
      position: {x: -0.06406123, y: 0.0070372773, z: -0.030024713}
      rotation: {x: 0.080078, y: -0.087642126, z: -0.015874794, w: 0.9928013}
      scale: {x: 1, y: 1.0000002, z: 0.99999964}
    - name: CH_FingerIn1_R
      parentName: CH_FingerIn0_R
      position: {x: -0.038644485, y: 0.0012197304, z: 0}
      rotation: {x: 0.00000039488063, y: 0.0000023543826, z: 0.031828728, w: 0.99949336}
      scale: {x: 0.99999976, y: 1.0000002, z: 1.0000001}
    - name: CH_FingerIn2_R
      parentName: CH_FingerIn1_R
      position: {x: -0.019831695, y: 0.00048395872, z: -0.00000015258789}
      rotation: {x: 0, y: 0, z: 0.15879549, w: 0.98731154}
      scale: {x: 1.0000001, y: 0.9999999, z: 0.99999976}
    - name: CH_FingerMid0_R
      parentName: CH_Hand_R
      position: {x: -0.065574415, y: 0.011041174, z: -0.008099975}
      rotation: {x: 0.010693147, y: -0.04940608, z: -0.019562306, w: 0.99853003}
      scale: {x: 0.9999994, y: 1.0000001, z: 0.99999917}
    - name: CH_FingerMid1_R
      parentName: CH_FingerMid0_R
      position: {x: -0.041185685, y: -0.0007039541, z: 0.00014663696}
      rotation: {x: -0.0005796822, y: -0.0005516259, z: 0.05480971, w: 0.9984965}
      scale: {x: 0.99999994, y: 1.0000007, z: 1.0000002}
    - name: CH_FingerMid2_R
      parentName: CH_FingerMid1_R
      position: {x: -0.02168396, y: 0.00048599957, z: 0.000107421874}
      rotation: {x: 0, y: 0, z: 0.18801899, w: 0.9821654}
      scale: {x: 0.99999964, y: 0.9999999, z: 1.0000001}
    - name: CH_FingerP0_R
      parentName: CH_Hand_R
      position: {x: -0.052998427, y: 0.004468865, z: 0.027333297}
      rotation: {x: 0.013489184, y: -0.0488795, z: -0.037809677, w: 0.99799764}
      scale: {x: 1.0000005, y: 0.9999999, z: 0.9999995}
    - name: CH_FingerP1_R
      parentName: CH_FingerP0_R
      position: {x: -0.030735472, y: -0.0015520048, z: 0}
      rotation: {x: 0.00000070128584, y: 0.000003870576, z: 0.07462892, w: 0.99721146}
      scale: {x: 1.0000001, y: 1.0000008, z: 1.0000004}
    - name: CH_FingerP2_R
      parentName: CH_FingerP1_R
      position: {x: -0.016274948, y: 0.0004840374, z: -0.00000015258789}
      rotation: {x: 0, y: 0, z: 0.21707165, w: 0.9761557}
      scale: {x: 0.99999994, y: 1.0000007, z: 1.0000004}
    - name: CH_FingerR0_R
      parentName: CH_Hand_R
      position: {x: -0.06365902, y: 0.010941544, z: 0.011445903}
      rotation: {x: 0.012175316, y: -0.04851308, z: -0.02769646, w: 0.99836427}
      scale: {x: 1.0000004, y: 1.0000001, z: 0.99999994}
    - name: CH_FingerR1_R
      parentName: CH_FingerR0_R
      position: {x: -0.037883643, y: -0.0011924744, z: 0}
      rotation: {x: 0.00000059138983, y: 0.0000035651026, z: 0.04982696, w: 0.9987579}
      scale: {x: 0.9999999, y: 0.99999994, z: 1.0000001}
    - name: CH_FingerR2_R
      parentName: CH_FingerR1_R
      position: {x: -0.01933979, y: -0.00000015258789, z: -0.00000015258789}
      rotation: {x: 0, y: 0, z: 0.20256762, w: 0.9792683}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
    - name: CH_FingerTh0_R
      parentName: CH_Hand_R
      position: {x: -0.006407836, y: -0.012739294, z: -0.020646468}
      rotation: {x: -0.6049892, y: -0.31676987, z: -0.31446207, w: 0.6593623}
      scale: {x: 0.99999994, y: 1.0000002, z: 0.99999946}
    - name: CH_FingerTh1_R
      parentName: CH_FingerTh0_R
      position: {x: -0.04057995, y: -0.00091072556, z: -0.0018042754}
      rotation: {x: 0.004750162, y: -0.019504545, z: 0.060898073, w: 0.9979421}
      scale: {x: 1.0000008, y: 0.9999999, z: 1.0000004}
    - name: CH_FingerTh2_R
      parentName: CH_FingerTh1_R
      position: {x: -0.02221054, y: 0.0008296919, z: 0.00000022888183}
      rotation: {x: 0, y: 0, z: 0.27279678, w: 0.9620717}
      scale: {x: 1.0000012, y: 0.9999992, z: 1.0000001}
    - name: CH_Thigh_L
      parentName: CH_Hips
      position: {x: -0.08826721, y: -0.014284587, z: 0.0023556517}
      rotation: {x: 0.66444254, y: 0.025450764, z: 0.74656063, w: 0.022706961}
      scale: {x: 1, y: 0.99999976, z: 0.9999996}
    - name: CH_Shin_L
      parentName: CH_Thigh_L
      position: {x: -0.007925682, y: 0.36190096, z: 0.007466621}
      rotation: {x: -0.05703204, y: -0.016010901, z: 0.09999778, w: 0.9932228}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000002}
    - name: CH_Foot_L
      parentName: CH_Shin_L
      position: {x: 0.029875869, y: 0.38749614, z: 0.008091049}
      rotation: {x: 0.046352614, y: 0.024369337, z: -0.31717333, w: 0.94692063}
      scale: {x: 1.0000007, y: 0.9999995, z: 1.0000001}
    - name: CH_Thigh_R
      parentName: CH_Hips
      position: {x: 0.08810974, y: -0.014284577, z: 0.0023555756}
      rotation: {x: 0.74175715, y: 0.024268737, z: 0.67003995, w: 0.01593194}
      scale: {x: 1, y: 0.9999999, z: 0.99999976}
    - name: CH_Shin_R
      parentName: CH_Thigh_R
      position: {x: -0.007925891, y: 0.36190093, z: -0.007466888}
      rotation: {x: 0.05868911, y: -0.0013824374, z: 0.08882158, w: 0.99431604}
      scale: {x: 1.0000007, y: 1.0000005, z: 0.99999976}
    - name: CH_Foot_R
      parentName: CH_Shin_R
      position: {x: 0.029875746, y: 0.38749602, z: -0.008090438}
      rotation: {x: -0.03313012, y: -0.033069517, z: -0.31789255, w: 0.9469705}
      scale: {x: 1.0000007, y: 1.0000004, z: 1.000001}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
