%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-2573086500142053668
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 12
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: LongSleeve
  m_Shader: {fileID: -6465566751694194690, guid: e0b73429f84a56840ba17b216237f806, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _DISABLE_SSR_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    IgnoreProjection: False
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _1st_ShadeMap:
        m_Texture: {fileID: 2800000, guid: f180dfef53ad0924882657e28833a60b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _2nd_ShadeMap:
        m_Texture: {fileID: 2800000, guid: 9cd90983043e3974e971a6c8d66cc42d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AngelRing_Sampler:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BakedNormal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: d6a0b548a02e12e4e954cb6c5bb20e70, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ClippingMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emissive_Tex:
        m_Texture: {fileID: 2800000, guid: dc71d88ff73488f45ac239b21f552745, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HighColor_Tex:
        m_Texture: {fileID: 2800000, guid: a01adb17bdc53ba4da348b3cba074154, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Main:
        m_Texture: {fileID: 2800000, guid: d1e50199c6f08ed46bf2d136ab225409, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d1e50199c6f08ed46bf2d136ab225409, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCap_Sampler:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapForMatCap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Outline_Sampler:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_1st_ShadePosition:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_2nd_ShadePosition:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_HighColorMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_MatcapMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_RimLightMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingGradeMap:
        m_Texture: {fileID: 2800000, guid: 7c75a3dfe8927774ea56a0777d1edcf1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _1st2nd_Shades_Feather: 0.776
    - _1st_ShadeColor_Feather: 0.335
    - _1st_ShadeColor_Step: 0.677
    - _2nd_ShadeColor_Feather: 0.776
    - _2nd_ShadeColor_Step: 0.618
    - _ARSampler_AlphaOn: 0
    - _AR_OffsetU: 0
    - _AR_OffsetV: 0.3
    - _AddPrecomputedVelocity: 0
    - _Add_Antipodean_RimLight: 0
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _AngelRing: 0
    - _Ap_RimLight_FeatherOff: 0
    - _Ap_RimLight_Power: 0.1
    - _AutoRenderQueue: 1
    - _BUILTIN_QueueControl: 1
    - _BUILTIN_QueueOffset: 0
    - _BaseColor_Step: 0.677
    - _BaseShade_Feather: 0.335
    - _Base_Speed: 0
    - _BlendMode: 0
    - _BlurLevelMatcap: 0
    - _BlurLevelSGM: 0
    - _BumpScale: 1
    - _BumpScaleMatcap: 1
    - _CameraRolling_Stabilizer: 0
    - _ClippingMode: 0
    - _Clipping_Level: 0
    - _ColorBoost: 1
    - _ColorShift_Speed: 0
    - _ConservativeDepthOffsetEnable: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DepthOffsetEnable: 0
    - _DetailNormalMapScale: 1
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EMISSIVE: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _Farthest_Distance: 0
    - _GI_Intensity: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _HighColor_Power: 0.48
    - _Inverse_Clipping: 0
    - _Inverse_MatcapMask: 0
    - _Inverse_Z_Axis_BLD: 1
    - _IsBaseMapAlphaAsClippingMask: 0
    - _Is_1st_ShadeColorOnly: 0
    - _Is_BLD: 0
    - _Is_BakedNormal: 0
    - _Is_BlendAddToHiColor: 1
    - _Is_BlendAddToMatCap: 1
    - _Is_BlendBaseColor: 1
    - _Is_ColorShift: 0
    - _Is_Filter_HiCutPointLightColor: 1
    - _Is_Filter_LightColor: 0
    - _Is_LightColor_1st_Shade: 1
    - _Is_LightColor_2nd_Shade: 1
    - _Is_LightColor_AR: 1
    - _Is_LightColor_Ap_RimLight: 1
    - _Is_LightColor_Base: 1
    - _Is_LightColor_HighColor: 0
    - _Is_LightColor_MatCap: 1
    - _Is_LightColor_Outline: 1
    - _Is_LightColor_RimLight: 1
    - _Is_NormalMap: 0
    - _Is_NormalMapForMatCap: 0
    - _Is_NormalMapToBase: 0
    - _Is_NormalMapToHighColor: 0
    - _Is_NormalMapToRimLight: 0
    - _Is_Ortho: 0
    - _Is_OutlineTex: 1
    - _Is_PingPong_Base: 0
    - _Is_SpecularToHighColor: 0
    - _Is_UseTweakHighColorOnShadow: 0
    - _Is_UseTweakMatCapOnShadow: 0
    - _Is_ViewCoord_Scroll: 0
    - _Is_ViewShift: 0
    - _LightDirection_MaskOn: 0
    - _MatCap: 0
    - _Metallic: 0
    - _Mode: 0
    - _Nearest_Distance: 1
    - _OUTLINE: 0
    - _OcclusionStrength: 1
    - _Offset_X_Axis_BLD: -0.05
    - _Offset_Y_Axis_BLD: 0.09
    - _Offset_Z: 2
    - _OpaqueCullMode: 2
    - _Outline_Width: 1.5
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _RimLight: 0
    - _RimLight_FeatherOff: 1
    - _RimLight_InsideMask: 0.47
    - _RimLight_Power: 0.01
    - _Rotate_EmissiveUV: 0
    - _Rotate_MatCapUV: 0
    - _Rotate_NormalMapForMatCapUV: 0
    - _SPRDefaultUnlitColorMask: 15
    - _SRPDefaultUnlitColMode: 1
    - _Scroll_EmissiveU: 0
    - _Scroll_EmissiveV: 0
    - _Set_SystemShadowsToBase: 1
    - _ShadeColor_Step: 0.618
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilComp: 0
    - _StencilMode: 0
    - _StencilNo: 1
    - _StencilOpFail: 0
    - _StencilOpPass: 0
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _StepOffset: 0
    - _SupportDecals: 1
    - _SurfaceType: 0
    - _TessEdgeLength: 5
    - _TessExtrusionAmount: 0
    - _TessPhongStrength: 0.5
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentEnabled: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _TweakHighColorOnShadow: 0
    - _TweakMatCapOnShadow: 0
    - _Tweak_HighColorMaskLevel: 0
    - _Tweak_LightDirection_MaskLevel: 0
    - _Tweak_MatCapUV: 0
    - _Tweak_MatcapMaskLevel: 0
    - _Tweak_RimLightMaskLevel: 0
    - _Tweak_ShadingGradeMapLevel: 0
    - _Tweak_SystemShadowsLevel: 0
    - _Tweak_transparency: 0
    - _UVSec: 0
    - _Unlit_Intensity: 1
    - _UseShadowThreshold: 0
    - _Use_1stAs2nd: 0
    - _Use_BaseAs1st: 0
    - _ZOverDrawMode: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - _ZWriteMode: 1
    - _simpleUI: 2
    - _utsTechnique: 1
    - _utsVersion: 2.07
    - _utsVersionX: 2
    - _utsVersionY: 2
    - _utsVersionZ: 0
    m_Colors:
    - _1st_ShadeColor: {r: 1, g: 1, b: 1, a: 1}
    - _2nd_ShadeColor: {r: 1, g: 1, b: 1, a: 1}
    - _AngelRing_Color: {r: 1, g: 1, b: 1, a: 1}
    - _Ap_RimLightColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorShift: {r: 0, g: 0, b: 0, a: 1}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _Emissive_Color: {r: 5.2780313, g: 5.2780313, b: 5.2780313, a: 1}
    - _HighColor: {r: 0.3962264, g: 0.33053476, b: 0.25978997, a: 1}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _Outline_Color: {r: 0, g: 0, b: 0, a: 1}
    - _RimLightColor: {r: 0, g: 0.8758622, b: 1, a: 1}
    - _Tiling: {r: 1, g: 1, b: 0, a: 0}
    - _ViewShift: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &242505081636331739
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
--- !u!114 &6621973610829896646
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 639247ca83abc874e893eb93af2b5e44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 0
