%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6084932891614280098
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6084932891614280109}
  - component: {fileID: 6084932891614280108}
  m_Layer: 0
  m_Name: FrontHairSetting
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &6084932891614280109
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6084932891614280098}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6084932892119144703}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6084932891614280108
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6084932891614280098}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bdbd3ce05f5b45942b56ede5c9b38364, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializeData:
    clothType: 1
    sourceRenderers: []
    paintMode: 0
    paintMaps: []
    rootBones: []
    connectionMode: 0
    rotationalInterpolation: 0.5
    rootRotation: 0.5
    animationBlendRatio: 0
    reductionSetting:
      simpleDistance: 0
      shapeDistance: 0
    customSkinningSetting:
      enable: 0
      skinningBones: []
    normalAlignmentSetting:
      alignmentMode: 2
      adjustmentTransform: {fileID: 0}
    normalAxis: 1
    gravity: 3
    gravityDirection:
      x: 0
      y: -1
      z: 0
    gravityFalloff: 0
    damping:
      value: 0.02
      useCurve: 0
      curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    radius:
      value: 0.02
      useCurve: 0
      curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inertiaConstraint:
      movementInertia: 1
      rotationInertia: 1
      depthInertia: 0
      centrifualAcceleration: 0
      movementSpeedLimit:
        value: 2
        use: 1
      rotationSpeedLimit:
        value: 357
        use: 1
      particleSpeedLimit:
        value: 3
        use: 1
    tetherConstraint:
      distanceCompression: 0.5
    distanceConstraint:
      stiffness:
        value: 1
        useCurve: 0
        curve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: -0.5
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0.5
            inSlope: -0.5
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    triangleBendingConstraint:
      stiffness: 0.5
    angleRestorationConstraint:
      useAngleRestoration: 1
      stiffness:
        value: 0.06
        useCurve: 1
        curve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: -0.7939491
            outSlope: -0.7939491
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 0.20605087
            inSlope: -0.7939491
            outSlope: -0.7939491
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      velocityAttenuation: 0.5
      gravityFalloff: 0
    angleLimitConstraint:
      useAngleLimit: 1
      limitAngle:
        value: 45
        useCurve: 1
        curve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0.48802948
            inSlope: 0.5119705
            outSlope: 0.5119705
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0.5119705
            outSlope: 0.5119705
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      stiffness: 0.5
    motionConstraint:
      useMaxDistance: 0
      maxDistance:
        value: 0.3
        useCurve: 0
        curve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      useBackstop: 1
      backstopRadius: 1
      backstopDistance:
        value: 0.02
        useCurve: 1
        curve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0.49590302
            inSlope: 0.504097
            outSlope: 0.504097
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0.504097
            outSlope: 0.504097
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      stiffness: 0.1
    colliderCollisionConstraint:
      mode: 1
      friction: 0.05
      colliderList: []
    selfCollisionConstraint:
      selfMode: 0
      surfaceThickness:
        value: 0.005
        useCurve: 0
        curve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0.5
            inSlope: 0
            outSlope: 0.5
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0.5
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      syncMode: 0
      syncPartner: {fileID: 0}
  serializeData2:
    selectionData:
      positions:
      - x: -0.00047761644
        y: 0.9474178
        z: -0.11212534
      - x: 0.003996298
        y: 0.7667136
        z: -0.124061346
      attributes:
      - Value: 1
      - Value: 2
      maxConnectionDistance: 0.18115321
      userEdit: 0
  gizmoSerializeData:
    always: 0
    clothDebugSettings:
      enable: 1
      ztest: 0
      position: 1
      axis: 0
      shape: 1
      baseLine: 0
      depth: 0
      collider: 1
      animatedPosition: 0
      animatedAxis: 0
      animatedShape: 0
--- !u!1 &6084932892119144701
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6084932892119144703}
  - component: {fileID: 6084932892119144700}
  m_Layer: 0
  m_Name: RuntimeBuildDemo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6084932892119144703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6084932892119144701}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6084932891614280109}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6084932892119144700
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6084932892119144701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0126e2925cecdf4a933c28a27f415ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  characterPrefab: {fileID: 3030792855753874699, guid: 2e7240c8003a64b478effd2a812f4d31, type: 3}
  frontHairSource: {fileID: 6084932891614280108}
  ribbonPresetName: MC2_BoneCloth_Ribbon_Demo
  skirtName: _body_summer
  skirtPaintMap: {fileID: 2800000, guid: 00d6a209303f1804a81aee79794da3a4, type: 3}
