using UnityEditor;

[InitializeOnLoad]
public class AutoEnableBurstCompilation
{
    static AutoEnableBurstCompilation()
    {
        if ((!EditorPrefs.GetBool("AutoEnableBurst", false)) && (!EditorPrefs.GetBool("BurstCompilation", false)))
        {
            EditorApplication.delayCall -= EnableBurstCompilation;
            EditorApplication.delayCall += EnableBurstCompilation;
        }
    }

    static void EnableBurstCompilation()
    {
        EditorApplication.delayCall -= EnableBurstCompilation;
        if (!EditorPrefs.GetBool("BurstCompilation", false))
        {
            EditorApplication.ExecuteMenuItem("Jobs/Burst/Enable Compilation");
            EditorPrefs.SetBool("AutoEnableBurst", true);
        }
    }
}
