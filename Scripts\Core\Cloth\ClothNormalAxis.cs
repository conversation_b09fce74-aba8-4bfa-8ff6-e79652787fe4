﻿// Magica Cloth 2.
// Copyright (c) 2023 MagicaSoft.
// https://magicasoft.jp
using UnityEngine;

namespace MagicaCloth2
{
    /// <summary>
    /// Axis to use as normal.
    /// 法線として利用する軸
    /// </summary>
    public enum ClothNormalAxis
    {
        [<PERSON><PERSON><PERSON>("Right (red)")]
        Right = 0,
        [<PERSON><PERSON><PERSON>("Up (green)")]
        Up = 1,
        [<PERSON><PERSON><PERSON>("Forward (blue)")]
        Forward = 2,
        [<PERSON><PERSON><PERSON>("Inverse Right (red)")]
        InverseRight = 3,
        [<PERSON><PERSON><PERSON>("Inverse Up (green)")]
        InverseUp = 4,
        [<PERSON><PERSON><PERSON>("Inverse Forward (blue)")]
        InverseForward = 5,
    }
}
