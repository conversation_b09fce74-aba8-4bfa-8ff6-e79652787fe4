﻿/------------------------------------------------------------------------------
// Magica Cloth 2
// Copyright (c) Magica Soft, 2023
// https://magicasoft.jp
//------------------------------------------------------------------------------

### Overview
MagicaCloth2 is a general-purpose cloth simulation that can be used in Unity.


### Support Unity versions
Unity2021.3.16 or higher


### Required Unity package
Burst 1.8.2 or higher
Collections 1.4.0 or higher
Mathematics 1.2.6 or higher


### Feature
* Fast cloth simulation with DOTS
* Works on all platforms except WebGL
* Supports both BoneCloth driven by bones (Transform) and MeshCloth driven by mesh vertices
* MeshCloth is also available for skinning meshes
* Not affected by render pipeline or shader
* With full source code


### Install
The necessary packages are automatically installed when importing Magica Cloth.
If you get an error, manually install the Burst/Collections/Mathematics package.
See [Required Unity package] for each package version.


### Documentation
Since it is an online manual, please refer to the following URL for details.
https://magicasoft.jp/en/magica-cloth-2-2/


### Support
Support email.
<EMAIL>

Unity Forum.
https://forum.unity.com/threads/coming-soon-magicacloth2-hybrid-cloth-simulation.1395865/

It would be helpful if you could include the versions of the Unity editor and MagicaCloth2 you are using to facilitate support.
You can check the version of MagicaCloth from the menu [Tools->Magica Cloth2->About].


### License
MagicaCloth2 uses Unity-chan as a key image and sample.
© Unity Technologies Japan/UCL
https://unity-chan.com/


### Release Notes
https://magicasoft.jp/en/mc2_releasenote/

