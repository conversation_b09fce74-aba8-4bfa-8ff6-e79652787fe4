﻿/------------------------------------------------------------------------------
// Magica Cloth 2
// Copyright (c) Magica Soft, 2023
// https://magicasoft.jp
//------------------------------------------------------------------------------

■概要
MagicaCloth2はUnityで利用できる汎用的なクロスシミュレーションです。


■要求Unityバージョン
Unity2021.3.16以上


■要求パッケージ
Burst 1.8.2以上
Collections 1.4.0以上
Mathematics 1.2.6以上


■機能
* DOTSによる高速なクロスシミュレーション
* WebGLを除くすべてのプラットフォームで動作可能
* ボーン(Transform)で駆動するBoneClothとメッシュ頂点で駆動するMeshClothの両方に対応
* MeshClothはスキニングメッシュでも利用可能
* レンダーパイプラインやシェーダーに影響されません
* フルソースコード付き


■インストール
Magica Clothインポート時に必要なパッケージは自動でインストールされます。
もしエラーが発生する場合はBurst / Collections / Mathematicsパッケージを手動でインストールしてください。
各パッケージバージョンは[要求パッケージ]を参照してください。


■マニュアル
オンラインマニュアルとなっていますので、詳しくは次のURLを参照してください。
https://magicasoft.jp/magica-cloth-2/


■サポート窓口
サポートメール
<EMAIL>

Unityフォーラム
https://forum.unity.com/threads/coming-soon-magicacloth2-hybrid-cloth-simulation.1395865/

サポートを円滑に進めるために使用しているUnityエディターとMagicaCloth2のバージョンを記載していただくと助かります。
MagicaClothのバージョンはメニュー[Tools->Magica Cloth2->About]から確認可能です。


■ライセンス
MagicaCloth2はキー画像およびサンプルとしてUnityちゃんを使用しています
© Unity Technologies Japan/UCL
https://unity-chan.com/


■更新履歴
https://magicasoft.jp/mc2_releasenote/

