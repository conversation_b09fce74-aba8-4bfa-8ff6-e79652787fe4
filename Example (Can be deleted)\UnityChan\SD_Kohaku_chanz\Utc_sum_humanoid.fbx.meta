fileFormatVersion: 2
guid: 5c78fba67bb448b42b8dc9ef978170e0
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: 1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Character1_Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_Spine1
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: bone_eye_L
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: bone_eye_R
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_Spine2
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Character1_RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Utc_sum_humanoid(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_Reference
      parentName: Utc_sum_humanoid(Clone)
      position: {x: -0, y: 0, z: -0.000000002661807}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_Hips
      parentName: Character1_Reference
      position: {x: -0, y: -0.049120344, z: 0.41803354}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_Spine
      parentName: Character1_Hips
      position: {x: -0, y: -0.00017591476, z: 0.001539917}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_Spine1
      parentName: Character1_Spine
      position: {x: -0, y: 0.000000009536743, z: 0.04283531}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_Spine2
      parentName: Character1_Spine1
      position: {x: -0, y: 0.00015314102, z: 0.041275635}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_LeftShoulder
      parentName: Character1_Spine2
      position: {x: -0.026731491, y: 0.046953294, z: 0.10265426}
      rotation: {x: 6.416709e-10, y: -0.14720343, z: -0.012063365, w: 0.9890327}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
    - name: Character1_LeftArm
      parentName: Character1_LeftShoulder
      position: {x: -0.046947505, y: -0.0003006166, z: 0.0036682128}
      rotation: {x: 0.025138553, y: 0.12145908, z: 0.00800092, w: 0.99224585}
      scale: {x: 1.0000001, y: 1.0000001, z: 0.9999998}
    - name: J_L_Arm_00_tw
      parentName: Character1_LeftArm
      position: {x: 0.00061920163, y: 0.0000003504753, z: 0.000030059813}
      rotation: {x: -0.019484477, y: -0.000087756256, z: -0.0000014493895, w: 0.99981016}
      scale: {x: 0.99999994, y: 0.9999999, z: 1.0000002}
    - name: Character1_LeftForeArm
      parentName: Character1_LeftArm
      position: {x: -0.11878532, y: 0.000000009536743, z: 0.000000076293944}
      rotation: {x: 0.012962691, y: 0.0029668726, z: 0.0336384, w: 0.99934566}
      scale: {x: 0.99999994, y: 0.99999994, z: 1.0000002}
    - name: Character1_LeftHand
      parentName: Character1_LeftForeArm
      position: {x: -0.112707324, y: -0.0000000047683715, z: -0.000000038146972}
      rotation: {x: -0.025144951, y: 0.0009263517, z: -0.000049031725, w: 0.9996834}
      scale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
    - name: Character1_LeftHandRing1
      parentName: Character1_LeftHand
      position: {x: -0.049339905, y: 0.008889142, z: 0.0041249082}
      rotation: {x: -0.0031778018, y: -0.008010217, z: 0.014246811, w: 0.9998614}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: Character1_LeftHandRing2
      parentName: Character1_LeftHandRing1
      position: {x: -0.013061695, y: 0.000000007152557, z: -0.000000038146972}
      rotation: {x: -0.005074419, y: -0.01721808, z: -0.011732383, w: 0.99977005}
      scale: {x: 0.99999976, y: 0.9999999, z: 0.9999999}
    - name: Character1_LeftHandRing3
      parentName: Character1_LeftHandRing2
      position: {x: -0.010001297, y: 0.0000000017881393, z: 0}
      rotation: {x: 6.765418e-17, y: -0.000000044703473, z: 0.0000000015133989, w: 1}
      scale: {x: 1.0000002, y: 1.0000001, z: 1}
    - name: Character1_LeftHandPinky1
      parentName: Character1_LeftHand
      position: {x: -0.046355017, y: 0.022602014, z: 0.0009494018}
      rotation: {x: 0.0022179454, y: -0.017407462, z: 0.05259034, w: 0.998462}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: Character1_LeftHandPinky2
      parentName: Character1_LeftHandPinky1
      position: {x: -0.009598923, y: 0.000000007152557, z: 0.000000076293944}
      rotation: {x: -0.0041052187, y: -0.02118038, z: -0.00477994, w: 0.99975586}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: Character1_LeftHandPinky3
      parentName: Character1_LeftHandPinky2
      position: {x: -0.0075915335, y: -0.000000009536743, z: 0}
      rotation: {x: -0.000000002793968, y: -0.000000029802322, z: -0.000000007450581, w: 1}
      scale: {x: 0.9999999, y: 1, z: 0.99999994}
    - name: Character1_LeftHandIndex1
      parentName: Character1_LeftHand
      position: {x: -0.046978243, y: -0.022182073, z: 0.0025979613}
      rotation: {x: 0.0048259627, y: -0.018998887, z: 0.045703307, w: 0.99876267}
      scale: {x: 0.99999994, y: 1.0000001, z: 1}
    - name: Character1_LeftHandIndex2
      parentName: Character1_LeftHandIndex1
      position: {x: -0.014227028, y: -0.00000000834465, z: 0.000000076293944}
      rotation: {x: 0.0058518304, y: 0.04137672, z: 0.0068952497, w: 0.9991027}
      scale: {x: 0.9999998, y: 0.9999998, z: 0.99999964}
    - name: Character1_LeftHandIndex3
      parentName: Character1_LeftHandIndex2
      position: {x: -0.0107357595, y: 0.000000014305114, z: -0.000000076293944}
      rotation: {x: -0.0000000018626456, y: -0.00000014901154, z: -0.0000000037252887, w: 1}
      scale: {x: 1.0000004, y: 0.9999999, z: 1.0000004}
    - name: Character1_LeftHandThumb1
      parentName: Character1_LeftHand
      position: {x: -0.016437054, y: -0.01568381, z: -0.017473105}
      rotation: {x: 0.6827791, y: 0.28976816, z: 0.23807746, w: 0.6270297}
      scale: {x: 0.9999999, y: 0.9999999, z: 1}
    - name: Character1_LeftHandThumb2
      parentName: Character1_LeftHandThumb1
      position: {x: -0.017500991, y: 0, z: -0.000000076293944}
      rotation: {x: -0.007327673, y: -0.020293944, z: 0.030798841, w: 0.99929273}
      scale: {x: 1, y: 0.9999998, z: 0.99999994}
    - name: Character1_LeftHandThumb3
      parentName: Character1_LeftHandThumb2
      position: {x: -0.010315971, y: 0, z: 0.00000007152557}
      rotation: {x: -0.01386693, y: -0.019060507, z: -0.00015807952, w: 0.9997222}
      scale: {x: 0.99999994, y: 0.9999999, z: 1}
    - name: Character1_LeftHandThumb4
      parentName: Character1_LeftHandThumb3
      position: {x: -0.012429466, y: -0.000000038146972, z: 0.000000038146972}
      rotation: {x: 0.00000014156107, y: 0.00000002607702, z: -0.000000089406974, w: 1}
      scale: {x: 1.0000001, y: 0.99999946, z: 0.9999998}
    - name: Character1_LeftHandMiddle1
      parentName: Character1_LeftHand
      position: {x: -0.05140888, y: -0.0063463617, z: 0.006106071}
      rotation: {x: 0.0023525557, y: -0.018158944, z: 0.040574767, w: 0.9990088}
      scale: {x: 1, y: 1.0000001, z: 0.99999976}
    - name: Character1_LeftHandMiddle2
      parentName: Character1_LeftHandMiddle1
      position: {x: -0.016781254, y: 0.000000019073486, z: 0}
      rotation: {x: -0.0038751021, y: 0.017281516, z: -0.010274473, w: 0.9997904}
      scale: {x: 1.0000002, y: 1, z: 1.0000001}
    - name: Character1_LeftHandMiddle3
      parentName: Character1_LeftHandMiddle2
      position: {x: -0.012807655, y: -0.000000005960464, z: -0.000000076293944}
      rotation: {x: 0.000000007450579, y: -0.000000014901163, z: -0.0000000037252907, w: 1}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: J_L_Elbow
      parentName: Character1_LeftForeArm
      position: {x: 0.00022022247, y: 0.0046890355, z: -0.00008010864}
      rotation: {x: -0.006511218, y: -0.0007156488, z: -0.017952144, w: 0.9998174}
      scale: {x: 1.0000001, y: 0.99999964, z: 0.9999998}
    - name: J_L_ForeArm_00_tw
      parentName: Character1_LeftForeArm
      position: {x: -0.10891037, y: 0.00009298801, z: -0.00038040162}
      rotation: {x: -0.012573468, y: 0.00046314506, z: -0.00002451689, w: 0.99992085}
      scale: {x: 1.0000002, y: 0.9999999, z: 1}
    - name: Character1_Neck
      parentName: Character1_Spine2
      position: {x: -0, y: 0.047267172, z: 0.1330844}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_Head
      parentName: Character1_Neck
      position: {x: 2.5015806e-10, y: -0.00804217, z: 0.030468559}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: utc_face
      parentName: Character1_Head
      position: {x: -0, y: 0.009918099, z: -0.6672374}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_L_HairSide2_00
      parentName: Character1_Head
      position: {x: -0.13977991, y: -0.032653578, z: 0.14276138}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_L_HairSide2_01
      parentName: J_L_HairSide2_00
      position: {x: -0, y: 0, z: -0.07184112}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_L_HairTail_00
      parentName: Character1_Head
      position: {x: -0.08453446, y: 0.12091128, z: 0.07472458}
      rotation: {x: 0.27495223, y: 0.14915642, z: 0.13304274, w: 0.94045377}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: J_L_HairTail_01
      parentName: J_L_HairTail_00
      position: {x: 0.000000019073486, y: -0.000000076293944, z: -0.19820912}
      rotation: {x: 0.000000029802326, y: -0.000000022351744, z: 0.000000011175873, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1.0000001}
    - name: J_L_HairTail_02
      parentName: J_L_HairTail_01
      position: {x: -0, y: 0.000000114440915, z: -0.2224917}
      rotation: {x: -4.9960047e-16, y: -0.00000006705523, z: -0.0000000074505815, w: 1}
      scale: {x: 0.9999999, y: 1.0000002, z: 0.99999994}
    - name: J_L_HairTail_03
      parentName: J_L_HairTail_02
      position: {x: 0.000000019073486, y: 0.000000076293944, z: -0.17191942}
      rotation: {x: 0.000000014901161, y: -0.000000007450581, z: -0.000000007450581, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_L_HeadRibbon_00
      parentName: Character1_Head
      position: {x: -0.079515405, y: -0.068754226, z: 0.3136406}
      rotation: {x: -0.2504739, y: -0.2833735, z: -0.049994256, w: 0.9243716}
      scale: {x: 0.99999994, y: 0.99999964, z: 0.99999976}
    - name: J_L_HeadRibbon_01
      parentName: J_L_HeadRibbon_00
      position: {x: 0.000000076293944, y: -0.000000114440915, z: 0.2478369}
      rotation: {x: -0.8290727, y: -0.12707953, z: 0.09669092, w: 0.5358546}
      scale: {x: 1, y: 1.0000001, z: 1.0000004}
    - name: J_L_HeadRibbon_02
      parentName: J_L_HeadRibbon_01
      position: {x: -0, y: 0.00000022888183, z: 0.13667938}
      rotation: {x: 0.68807155, y: -0.053607468, z: -0.044375025, w: 0.7222982}
      scale: {x: 1, y: 0.9999997, z: 0.99999994}
    - name: J_L_HeadRibbon_03
      parentName: J_L_HeadRibbon_02
      position: {x: -0.000000076293944, y: -0.00000015258789, z: 0.16675673}
      rotation: {x: -0.00000022351746, y: -0.00000011920929, z: 0.0000000149011345, w: 1}
      scale: {x: 1, y: 0.9999999, z: 1.0000001}
    - name: J_R_HairSide2_00
      parentName: Character1_Head
      position: {x: 0.13977984, y: -0.032653518, z: 0.14276077}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_R_HairSide2_01
      parentName: J_R_HairSide2_00
      position: {x: -0, y: 0, z: -0.07184112}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_R_HairTail_00
      parentName: Character1_Head
      position: {x: 0.08453457, y: 0.12091121, z: 0.074723355}
      rotation: {x: 0.27495188, y: -0.14915672, z: -0.13304274, w: 0.9404538}
      scale: {x: 1, y: 1, z: 1}
    - name: J_R_HairTail_01
      parentName: J_R_HairTail_00
      position: {x: -0.000000095367426, y: 0.0000009918213, z: -0.19820796}
      rotation: {x: 0.000000029802322, y: -0.0000000074505815, z: -0.00000010058282, w: 1}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: J_R_HairTail_02
      parentName: J_R_HairTail_01
      position: {x: -0.0000004196167, y: -0.0000005340576, z: -0.2224927}
      rotation: {x: 0.000000089406925, y: 0.000000014901161, z: -0.000000040978207, w: 1}
      scale: {x: 0.99999964, y: 1.0000002, z: 1}
    - name: J_R_HairTail_03
      parentName: J_R_HairTail_02
      position: {x: -0.000000038146972, y: -0.000000038146972, z: -0.17191932}
      rotation: {x: -1.6653337e-16, y: -0.000000014901158, z: -0.000000011175868, w: 1}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: J_R_HeadRibbon_00
      parentName: Character1_Head
      position: {x: 0.0795154, y: -0.06875421, z: 0.31363952}
      rotation: {x: -0.25047436, y: 0.28337345, z: 0.04999436, w: 0.92437154}
      scale: {x: 0.9999998, y: 1, z: 1}
    - name: J_R_HeadRibbon_01
      parentName: J_R_HeadRibbon_00
      position: {x: -0.0000011062622, y: -0.0000010299682, z: 0.24783768}
      rotation: {x: -0.82907134, y: 0.12707938, z: -0.096691504, w: 0.5358566}
      scale: {x: 1.0000002, y: 1.0000001, z: 0.99999994}
    - name: J_R_HeadRibbon_02
      parentName: J_R_HeadRibbon_01
      position: {x: 0.0000008010864, y: 0.000001335144, z: 0.13668053}
      rotation: {x: 0.68807024, y: 0.05360729, z: 0.0443758, w: 0.7222994}
      scale: {x: 1.0000002, y: 0.9999999, z: 1.0000001}
    - name: J_R_HeadRibbon_03
      parentName: J_R_HeadRibbon_02
      position: {x: -0.0000009536743, y: -0.0000010681152, z: 0.16675788}
      rotation: {x: 0.0000007748599, y: -0.00000043213427, z: -0.00000084936573, w: 1}
      scale: {x: 1.0000001, y: 1, z: 0.9999997}
    - name: J_L_HairSide_00
      parentName: Character1_Head
      position: {x: -0.07298515, y: -0.0934125, z: 0.27946135}
      rotation: {x: 0.032767124, y: -0.43403226, z: -0.092962936, w: 0.89548886}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: J_L_HairSide_01
      parentName: J_L_HairSide_00
      position: {x: -0.124109186, y: 0.000000047683713, z: -0.00000030517577}
      rotation: {x: 0.029233634, y: -0.33951724, z: -0.06338306, w: 0.93800646}
      scale: {x: 0.99999976, y: 1.0000002, z: 1.0000001}
    - name: J_L_HairSide_02
      parentName: J_L_HairSide_01
      position: {x: -0.17118454, y: 0.000000009536743, z: 0.00000022888183}
      rotation: {x: 0.000000077299774, y: -0.00000008475035, z: -0.000000014901153, w: 1}
      scale: {x: 1.0000002, y: 1, z: 0.9999998}
    - name: J_R_HairSide_00
      parentName: Character1_Head
      position: {x: 0.072985135, y: -0.09341254, z: 0.27946243}
      rotation: {x: 0.03276742, y: 0.4340327, z: 0.09296258, w: 0.8954887}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: J_R_HairSide_01
      parentName: J_R_HairSide_00
      position: {x: 0.124110945, y: -0.00000016212464, z: -0.0000010681152}
      rotation: {x: 0.029233143, y: 0.33951682, z: 0.063383006, w: 0.9380066}
      scale: {x: 1.0000001, y: 1, z: 1.0000002}
    - name: J_R_HairSide_02
      parentName: J_R_HairSide_01
      position: {x: 0.17118447, y: 0.000000009536743, z: -0.00000016212464}
      rotation: {x: -0.00000069662923, y: 0.00000008195637, z: 0.000000029802383, w: 1}
      scale: {x: 0.9999999, y: 1.0000001, z: 0.9999996}
    - name: bone_eye_L
      parentName: Character1_Head
      position: {x: -0.026893321, y: 0.04649828, z: 0.102056116}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: bone_eye_R
      parentName: Character1_Head
      position: {x: 0.026893293, y: 0.046498314, z: 0.10205597}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_L_HairFront_00
      parentName: Character1_Head
      position: {x: 0.00047761618, y: -0.102207325, z: 0.28018036}
      rotation: {x: -0.032923594, y: 0.012465945, z: 0.003332434, w: 0.99937457}
      scale: {x: 1, y: 0.99999994, z: 0.9999999}
    - name: J_L_HairFront_01
      parentName: J_L_HairFront_00
      position: {x: 0.0000000023841857, y: 0, z: -0.18115325}
      rotation: {x: 0.14653109, y: -0.012764544, z: -0.0018909584, w: 0.9891219}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
    - name: J_R_HairFront_00
      parentName: Character1_Head
      position: {x: 0.014399265, y: -0.09940759, z: 0.28018036}
      rotation: {x: -0.06683042, y: -0.07431372, z: 0.0030237695, w: 0.9949885}
      scale: {x: 1, y: 0.99999994, z: 0.9999999}
    - name: J_R_HairFront_01
      parentName: J_R_HairFront_00
      position: {x: -0.000000028610229, y: 0.000000019073486, z: -0.18115318}
      rotation: {x: 0.14653113, y: -0.012764582, z: -0.0018909615, w: 0.9891219}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: Collar
      parentName: Character1_Spine2
      position: {x: -0, y: 0.08650589, z: 0.12216255}
      rotation: {x: 0.000000014901161, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Ribbon_L
      parentName: Character1_Spine2
      position: {x: -0.004597386, y: -0.05966402, z: 0.048371542}
      rotation: {x: 0.000000014901161, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_RightShoulder
      parentName: Character1_Spine2
      position: {x: 0.02673147, y: 0.046953294, z: 0.1026548}
      rotation: {x: 0.000000017829457, y: 0.14720337, z: 0.012063239, w: 0.9890327}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: Character1_RightArm
      parentName: Character1_RightShoulder
      position: {x: 0.04694744, y: -0.0003006053, z: 0.0036683274}
      rotation: {x: 0.025114838, y: -0.12146138, z: -0.008003939, w: 0.9922461}
      scale: {x: 1.0000001, y: 1.0000001, z: 1}
    - name: J_R_Arm_00_tw
      parentName: Character1_RightArm
      position: {x: -0.0006183624, y: 0.00000034093856, z: 0.000029029845}
      rotation: {x: -0.019467765, y: 0.0000867438, z: 0.0000013984002, w: 0.9998105}
      scale: {x: 0.99999994, y: 0.9999999, z: 1.0000001}
    - name: Character1_RightForeArm
      parentName: Character1_RightArm
      position: {x: 0.11878662, y: -0.0000000023841857, z: -0.0000005340576}
      rotation: {x: 0.012962643, y: -0.0029668876, z: -0.03363779, w: 0.9993456}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Character1_RightHand
      parentName: Character1_RightForeArm
      position: {x: 0.11270626, y: -0.00000007152557, z: -0.0000009536743}
      rotation: {x: -0.024933074, y: -0.0007821354, z: 0.000042964955, w: 0.9996888}
      scale: {x: 0.99999994, y: 1, z: 1.0000004}
    - name: Character1_RightHandThumb1
      parentName: Character1_RightHand
      position: {x: 0.016436901, y: -0.015683817, z: -0.017472915}
      rotation: {x: 0.68275243, y: -0.28985518, z: -0.23803082, w: 0.6270362}
      scale: {x: 0.9999999, y: 1, z: 1.0000001}
    - name: Character1_RightHandThumb2
      parentName: Character1_RightHandThumb1
      position: {x: 0.017501583, y: 0.000000114440915, z: -0.00000050544736}
      rotation: {x: -0.0073244264, y: 0.020301742, z: -0.030826923, w: 0.9992917}
      scale: {x: 1.0000001, y: 0.9999997, z: 0.99999994}
    - name: Character1_RightHandThumb3
      parentName: Character1_RightHandThumb2
      position: {x: 0.010316124, y: 0.0000005340576, z: -0.00000010967254}
      rotation: {x: -0.01386646, y: 0.019060291, z: 0.00015806762, w: 0.9997222}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
    - name: Character1_RightHandThumb4
      parentName: Character1_RightHandThumb3
      position: {x: 0.012428941, y: -0.0000006866455, z: 0.00000018596648}
      rotation: {x: 0.00000030919912, y: 0.000000108033525, z: 0.00000022351746, w: 1}
      scale: {x: 0.99999964, y: 0.9999999, z: 0.9999998}
    - name: Character1_RightHandPinky1
      parentName: Character1_RightHand
      position: {x: 0.046354044, y: 0.022601992, z: 0.00095024105}
      rotation: {x: 0.00222267, y: 0.017453691, z: -0.052590676, w: 0.9984611}
      scale: {x: 0.9999998, y: 1.0000001, z: 1}
    - name: Character1_RightHandPinky2
      parentName: Character1_RightHandPinky1
      position: {x: 0.009599628, y: 0.00000007390976, z: 0.0000011444091}
      rotation: {x: -0.0041054687, y: 0.021180103, z: 0.004780198, w: 0.99975586}
      scale: {x: 1.0000004, y: 1, z: 1.0000001}
    - name: Character1_RightHandPinky3
      parentName: Character1_RightHandPinky2
      position: {x: 0.0075922012, y: 0.00000013589859, z: -0.00000030517577}
      rotation: {x: -0.00000024121255, y: 0.00000025331983, z: -0.000000014901105, w: 1}
      scale: {x: 0.99999964, y: 1.0000001, z: 1}
    - name: Character1_RightHandRing1
      parentName: Character1_RightHand
      position: {x: 0.049340207, y: 0.008889168, z: 0.0041255187}
      rotation: {x: -0.0031775192, y: 0.008009321, z: -0.014247004, w: 0.9998615}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: Character1_RightHandRing2
      parentName: Character1_RightHandRing1
      position: {x: 0.013061752, y: 0.000000005960464, z: 0.00000015258789}
      rotation: {x: -0.0050746724, y: 0.017218595, z: 0.01173241, w: 0.99977005}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.9999997}
    - name: Character1_RightHandRing3
      parentName: Character1_RightHandRing2
      position: {x: 0.0100004, y: -0.000000009834766, z: -0.00000034332274}
      rotation: {x: 0.0000002421438, y: 0.000000029802315, z: -0.0000000022118978, w: 1}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: Character1_RightHandIndex1
      parentName: Character1_RightHand
      position: {x: 0.046977613, y: -0.02218206, z: 0.002598419}
      rotation: {x: 0.0048251827, y: 0.018980803, z: -0.045706283, w: 0.9987629}
      scale: {x: 1.0000001, y: 1.0000001, z: 0.99999994}
    - name: Character1_RightHandIndex2
      parentName: Character1_RightHandIndex1
      position: {x: 0.014227294, y: 0.00000006437301, z: -0.00000015258789}
      rotation: {x: 0.0058521065, y: -0.04137672, z: -0.0068952506, w: 0.9991027}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.99999976}
    - name: Character1_RightHandIndex3
      parentName: Character1_RightHandIndex2
      position: {x: 0.010736103, y: -0.000000085830685, z: -0.00000015258789}
      rotation: {x: -0.000000007450581, y: 0.000000104308135, z: -0.000000016763806, w: 1}
      scale: {x: 0.9999998, y: 0.9999999, z: 1.0000004}
    - name: Character1_RightHandMiddle1
      parentName: Character1_RightHand
      position: {x: 0.051409397, y: -0.006346327, z: 0.006106949}
      rotation: {x: 0.002352232, y: 0.018152952, z: -0.040573914, w: 0.9990089}
      scale: {x: 1.0000001, y: 1.0000002, z: 0.99999994}
    - name: Character1_RightHandMiddle2
      parentName: Character1_RightHandMiddle1
      position: {x: 0.01678072, y: -0.000000042915342, z: 0.000000076293944}
      rotation: {x: -0.0038748826, y: -0.017280553, z: 0.010274474, w: 0.9997904}
      scale: {x: 0.9999999, y: 0.99999994, z: 1.0000001}
    - name: Character1_RightHandMiddle3
      parentName: Character1_RightHandMiddle2
      position: {x: 0.012807674, y: 0.0000000667572, z: -0.00000087738033}
      rotation: {x: -0.0000000018626964, y: -0.00000092387177, z: -0.000000055879344, w: 1}
      scale: {x: 1.0000001, y: 1, z: 0.9999999}
    - name: J_R_ForeArm_00_tw
      parentName: Character1_RightForeArm
      position: {x: 0.1089093, y: 0.00009293079, z: -0.00038059233}
      rotation: {x: -0.012467529, y: -0.0003910812, z: 0.000021479957, w: 0.9999222}
      scale: {x: 0.9999999, y: 1, z: 1.0000004}
    - name: J_R_Elbow
      parentName: Character1_RightForeArm
      position: {x: -0.00022066115, y: 0.0046890113, z: -0.000079879756}
      rotation: {x: -0.0065112305, y: 0.00071536575, z: 0.017952092, w: 0.9998174}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000005}
    - name: Ribbon_R
      parentName: Character1_Spine2
      position: {x: 0.00459739, y: -0.059663896, z: 0.04837158}
      rotation: {x: 0.000000014901184, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt11_L
      parentName: Character1_Hips
      position: {x: -0.08134019, y: -0.07278677, z: 0.017924575}
      rotation: {x: -0.08120375, y: 0.06756295, z: -0.00025305356, w: 0.9944049}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt12_L
      parentName: Skirt11_L
      position: {x: -0, y: 0, z: -0.06362396}
      rotation: {x: 0, y: 0.000000007450581, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt13_L
      parentName: Skirt12_L
      position: {x: -0, y: 0, z: -0.071057506}
      rotation: {x: 0, y: 0.000000007450581, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt11_R
      parentName: Character1_Hips
      position: {x: 0.081340194, y: -0.07278665, z: 0.017924422}
      rotation: {x: -0.08120376, y: -0.06756316, z: 0.00025289616, w: 0.9944049}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt12_R
      parentName: Skirt11_R
      position: {x: -0.000000019073486, y: -0.00000020980835, z: -0.06362419}
      rotation: {x: 0.00000006705523, y: -0.000000014901163, z: 9.992008e-16, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt13_R
      parentName: Skirt12_R
      position: {x: -0.000000019073486, y: 0.000000057220458, z: -0.07105742}
      rotation: {x: -0.000000007450581, y: -0.000000014901163, z: 9.313225e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt20_L
      parentName: Character1_Hips
      position: {x: -0.11579351, y: 0.002001705, z: 0.025276184}
      rotation: {x: 0, y: 0.2754431, z: -0, w: 0.96131736}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt21_L
      parentName: Skirt20_L
      position: {x: -0.000000038146972, y: 0, z: -0.07196762}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt22_L
      parentName: Skirt21_L
      position: {x: 0.000000038146972, y: 0, z: -0.079026185}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt30_L
      parentName: Character1_Hips
      position: {x: -0.068905, y: 0.06840348, z: 0.025949707}
      rotation: {x: 0.16488287, y: 0.07006175, z: -0.008561788, w: 0.9837844}
      scale: {x: 1.0000001, y: 0.9999999, z: 0.99999994}
    - name: Skirt31_L
      parentName: Skirt30_L
      position: {x: -0, y: 0, z: -0.06441368}
      rotation: {x: 0, y: -0.0000000074505815, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt32_L
      parentName: Skirt31_L
      position: {x: -0, y: 0, z: -0.070049495}
      rotation: {x: 0, y: -0.0000000074505815, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt30_R
      parentName: Character1_Hips
      position: {x: 0.068904996, y: 0.06840344, z: 0.025949402}
      rotation: {x: 0.16488269, y: -0.07006178, z: 0.008561828, w: 0.98378444}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt31_R
      parentName: Skirt30_R
      position: {x: 0.000000038146972, y: 0.00000020980835, z: -0.06441322}
      rotation: {x: 0.000000014901163, y: -5.551116e-17, z: -0.0000000037252903, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt32_R
      parentName: Skirt31_R
      position: {x: 0.000000057220458, y: 0.000000047683713, z: -0.07004946}
      rotation: {x: 0.000000014901163, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt20_R
      parentName: Character1_Hips
      position: {x: 0.115793996, y: 0.0020017338, z: 0.02527649}
      rotation: {x: 0, y: -0.27544278, z: -0, w: 0.9613175}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt21_R
      parentName: Skirt20_R
      position: {x: -0.00000057220456, y: 0, z: -0.071967945}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skirt22_R
      parentName: Skirt21_R
      position: {x: 0.000000038146972, y: 0, z: -0.0790262}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_LeftUpLeg
      parentName: Character1_Hips
      position: {x: -0.09838927, y: 0.0002046585, z: -0.06464721}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_LeftLeg
      parentName: Character1_LeftUpLeg
      position: {x: -0, y: 0.021834202, z: -0.15186352}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_LeftFoot
      parentName: Character1_LeftLeg
      position: {x: -0, y: 0.03391635, z: -0.16433224}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_LeftToeBase
      parentName: Character1_LeftFoot
      position: {x: -0.00009206772, y: -0.044022497, z: -0.041161656}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_L_knee
      parentName: Character1_LeftLeg
      position: {x: -0, y: 0.00028729916, z: -0.0016981125}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_RightUpLeg
      parentName: Character1_Hips
      position: {x: 0.09838916, y: 0.0002045536, z: -0.06464706}
      rotation: {x: 0.0000000050956612, y: -0.00002997581, z: 0.00016999245, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_RightLeg
      parentName: Character1_RightUpLeg
      position: {x: -0, y: 0.02183431, z: -0.1518638}
      rotation: {x: 8.881784e-16, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_RightFoot
      parentName: Character1_RightLeg
      position: {x: -0, y: 0.033916343, z: -0.16433214}
      rotation: {x: -0.00000000509566, y: 0.000029975812, z: -0.00016999245, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character1_RightToeBase
      parentName: Character1_RightFoot
      position: {x: 0.00009202957, y: -0.044022463, z: -0.041161664}
      rotation: {x: 0, y: 0, z: 6.1098625e-13, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: J_R_knee
      parentName: Character1_RightUpLeg
      position: {x: -0, y: 0.022121582, z: -0.15356168}
      rotation: {x: 0.0000000025893838, y: 0.000000009194991, z: 0.0000000023283064, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: utc_head
      parentName: Utc_sum_humanoid(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: utc_Fhair
      parentName: Utc_sum_humanoid(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: utc_eye
      parentName: Utc_sum_humanoid(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: utc_Fhair2
      parentName: Utc_sum_humanoid(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: _body_summer
      parentName: Utc_sum_humanoid(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
